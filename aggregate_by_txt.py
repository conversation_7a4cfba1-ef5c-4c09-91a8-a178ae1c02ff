#!/usr/bin/env python3
"""
根据txt文件中的server_id列表，聚合对应的CSV数据并按时间排序
"""

import csv
import os
import glob
import pandas as pd
from pathlib import Path

def read_txt_file(txt_file):
    """
    读取txt文件，提取server_id和名字

    Args:
        txt_file: txt文件路径

    Returns:
        list: [(server_id, name), ...]
    """
    server_info = []

    with open(txt_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:  # 跳过空行
                parts = line.split('\t')
                if len(parts) >= 2:
                    server_id = parts[0].strip()
                    name = parts[1].strip()
                    server_info.append((server_id, name))
                elif len(parts) == 1:
                    # 如果只有一列，可能是用空格分隔的
                    parts = line.split()
                    if len(parts) >= 2:
                        server_id = parts[0].strip()
                        name = ' '.join(parts[1:]).strip()
                        server_info.append((server_id, name))

    return server_info

def aggregate_csv_data(server_ids, csv_dir="csv_files"):
    """
    聚合多个server_id对应的CSV数据，按create_time分组求和

    Args:
        server_ids: server_id列表
        csv_dir: CSV文件目录

    Returns:
        pandas.DataFrame: 聚合后的数据
    """
    all_data = []

    for server_id in server_ids:
        csv_file = os.path.join(csv_dir, f"server_{server_id}.csv")

        if os.path.exists(csv_file):
            print(f"  读取文件: {csv_file}")
            try:
                df = pd.read_csv(csv_file)
                all_data.append(df)
            except Exception as e:
                print(f"  警告: 读取文件 {csv_file} 时出错: {e}")
        else:
            print(f"  警告: 文件不存在: {csv_file}")

    if all_data:
        # 合并所有数据
        combined_df = pd.concat(all_data, ignore_index=True)

        # 移除 id 和 server_id 列
        columns_to_drop = ['id', 'server_id']
        for col in columns_to_drop:
            if col in combined_df.columns:
                combined_df = combined_df.drop(columns=[col])

        # 按 create_time 分组，对数值列求和
        numeric_columns = combined_df.select_dtypes(include=['number']).columns.tolist()

        # 确保 create_time 不在数值列中
        if 'create_time' in numeric_columns:
            numeric_columns.remove('create_time')

        # 按 create_time 分组并求和
        if numeric_columns:
            aggregated_df = combined_df.groupby('create_time')[numeric_columns].sum().reset_index()

            # 按 create_time 排序
            aggregated_df = aggregated_df.sort_values('create_time')

            return aggregated_df
        else:
            print("  警告: 没有找到可聚合的数值列")
            return pd.DataFrame()
    else:
        return pd.DataFrame()

def process_txt_files():
    """
    处理所有txt文件
    """
    # 查找所有txt文件
    txt_files = glob.glob("*.txt")

    if not txt_files:
        print("未找到任何txt文件")
        return

    print(f"找到 {len(txt_files)} 个txt文件")

    for txt_file in txt_files:
        print(f"\n处理文件: {txt_file}")

        # 读取txt文件内容
        server_info = read_txt_file(txt_file)

        if not server_info:
            print(f"  警告: {txt_file} 中没有找到有效的server_id")
            continue

        print(f"  找到 {len(server_info)} 个server_id:")
        for server_id, name in server_info:
            print(f"    {server_id}: {name}")

        # 提取server_id列表
        server_ids = [info[0] for info in server_info]

        # 聚合CSV数据
        print(f"  正在聚合数据...")
        combined_df = aggregate_csv_data(server_ids)

        if not combined_df.empty:
            # 生成输出文件名
            output_file = txt_file.replace('.txt', '.csv')

            print(f"  保存聚合数据到: {output_file}")
            print(f"  总记录数: {len(combined_df)}")

            # 保存CSV文件
            combined_df.to_csv(output_file, index=False, encoding='utf-8')

            # 显示时间范围
            if 'create_time' in combined_df.columns:
                min_time = combined_df['create_time'].min()
                max_time = combined_df['create_time'].max()
                print(f"  时间范围: {min_time} - {max_time}")
        else:
            print(f"  警告: 没有找到对应的数据")

if __name__ == "__main__":
    process_txt_files()
    print("\n处理完成！")
