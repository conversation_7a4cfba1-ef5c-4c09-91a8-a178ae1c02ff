#!/usr/bin/env python3
"""
解决matplotlib中文显示问题的脚本
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import pandas as pd
from datetime import datetime

def setup_chinese_font():
    """设置中文字体"""
    # 尝试多种中文字体设置方法
    try:
        # 方法1: 直接设置字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 方法2: 查找系统中的中文字体
        font_list = fm.findSystemFonts(fontpaths=None, fontext='ttf')
        chinese_fonts = []
        
        for font_path in font_list:
            try:
                font_prop = fm.FontProperties(fname=font_path)
                font_name = font_prop.get_name()
                if any(keyword in font_name.lower() for keyword in ['simhei', 'yahei', 'wenquanyi', 'noto', 'droid']):
                    chinese_fonts.append(font_name)
            except:
                continue
        
        if chinese_fonts:
            plt.rcParams['font.sans-serif'] = [chinese_fonts[0]] + plt.rcParams['font.sans-serif']
            print(f"找到中文字体: {chinese_fonts[0]}")
            return True
        else:
            print("未找到中文字体，将使用英文标签")
            return False
            
    except Exception as e:
        print(f"字体设置失败: {e}")
        return False

def create_charts_with_english_labels():
    """使用英文标签创建图表"""
    
    # 读取数据
    files = {
        'HuangJingLong_MX': 'huangjinglong_mx.csv',
        'XiaoXinYuan_Coinex': 'xiaoxinyuan_coinex.csv', 
        'HuangJingLong_Funding': 'huangjinglong_funding.csv'
    }
    
    data = {}
    for name, file in files.items():
        try:
            df = pd.read_csv(file)
            df['datetime'] = pd.to_datetime(df['create_time'], unit='s')
            data[name] = df
            print(f"{name}: {len(df)} records")
        except Exception as e:
            print(f"Error reading {file}: {e}")
            continue
    
    if not data:
        print("No data files found!")
        return
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
    
    # 1. Balance chart
    plt.figure(figsize=(15, 8))
    for i, (name, df) in enumerate(data.items()):
        plt.plot(df['datetime'], df['balance'], 
                 label=name, color=colors[i], linewidth=2, alpha=0.8)
    
    plt.title('Balance Over Time', fontsize=16, fontweight='bold')
    plt.xlabel('Time', fontsize=12)
    plt.ylabel('Balance', fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()
    
    # 2. Profit chart
    plt.figure(figsize=(15, 8))
    for i, (name, df) in enumerate(data.items()):
        plt.plot(df['datetime'], df['profit'], 
                 label=name, color=colors[i], linewidth=2, alpha=0.8)
    
    plt.title('Profit Over Time', fontsize=16, fontweight='bold')
    plt.xlabel('Time', fontsize=12)
    plt.ylabel('Profit', fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()
    
    # 3. Volume chart
    plt.figure(figsize=(15, 8))
    for i, (name, df) in enumerate(data.items()):
        plt.plot(df['datetime'], df['volume'], 
                 label=name, color=colors[i], linewidth=2, alpha=0.8)
    
    plt.title('Volume Over Time', fontsize=16, fontweight='bold')
    plt.xlabel('Time', fontsize=12)
    plt.ylabel('Volume', fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()
    
    # 4. Comprehensive comparison
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    fig.suptitle('Comprehensive Analysis', fontsize=18, fontweight='bold')
    
    # Balance
    for i, (name, df) in enumerate(data.items()):
        axes[0, 0].plot(df['datetime'], df['balance'], 
                        label=name, color=colors[i], linewidth=2, alpha=0.8)
    axes[0, 0].set_title('Balance', fontsize=14)
    axes[0, 0].set_ylabel('Balance', fontsize=12)
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Profit
    for i, (name, df) in enumerate(data.items()):
        axes[0, 1].plot(df['datetime'], df['profit'], 
                        label=name, color=colors[i], linewidth=2, alpha=0.8)
    axes[0, 1].set_title('Profit', fontsize=14)
    axes[0, 1].set_ylabel('Profit', fontsize=12)
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Volume
    for i, (name, df) in enumerate(data.items()):
        axes[1, 0].plot(df['datetime'], df['volume'], 
                        label=name, color=colors[i], linewidth=2, alpha=0.8)
    axes[1, 0].set_title('Volume', fontsize=14)
    axes[1, 0].set_ylabel('Volume', fontsize=12)
    axes[1, 0].set_xlabel('Time', fontsize=12)
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # Total Profit
    for i, (name, df) in enumerate(data.items()):
        axes[1, 1].plot(df['datetime'], df['profit_all'], 
                        label=name, color=colors[i], linewidth=2, alpha=0.8)
    axes[1, 1].set_title('Total Profit', fontsize=14)
    axes[1, 1].set_ylabel('Total Profit', fontsize=12)
    axes[1, 1].set_xlabel('Time', fontsize=12)
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # Adjust x-axis labels
    for ax in axes.flat:
        ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("设置中文字体...")
    chinese_support = setup_chinese_font()
    
    print("创建图表...")
    create_charts_with_english_labels()
    
    print("图表创建完成！")
