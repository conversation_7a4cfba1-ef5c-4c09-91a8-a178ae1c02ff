#!/usr/bin/env python3
"""
脚本用于将 live_summary.dump 文件按照 server_id 分成多个 CSV 文件
保留所有列的内容
"""

import csv
import os
from collections import defaultdict

def split_dump_by_server_id(input_file, output_dir="csv_files"):
    """
    将 dump 文件按照 server_id 分成多个 CSV 文件
    
    Args:
        input_file: 输入的 dump 文件路径
        output_dir: 输出 CSV 文件的目录
    """
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 用于存储每个 server_id 的数据
    server_data = defaultdict(list)
    
    print(f"正在读取文件: {input_file}")
    
    # 读取文件
    with open(input_file, 'r', encoding='utf-8') as f:
        # 读取第一行作为表头
        header = f.readline().strip().split('\t')
        print(f"表头: {header}")
        
        # 读取数据行
        line_count = 0
        for line in f:
            line_count += 1
            if line_count % 10000 == 0:
                print(f"已处理 {line_count} 行...")
            
            # 分割数据
            data = line.strip().split('\t')
            
            # 确保数据完整
            if len(data) == len(header):
                server_id = data[1]  # server_id 在第二列（索引1）
                server_data[server_id].append(data)
            else:
                print(f"警告: 第 {line_count + 1} 行数据不完整，跳过")
    
    print(f"总共处理了 {line_count} 行数据")
    print(f"发现 {len(server_data)} 个不同的 server_id")
    
    # 为每个 server_id 创建 CSV 文件
    for server_id, rows in server_data.items():
        output_file = os.path.join(output_dir, f"server_{server_id}.csv")
        
        print(f"正在创建文件: {output_file} (包含 {len(rows)} 行数据)")
        
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入表头
            writer.writerow(header)
            
            # 写入数据
            writer.writerows(rows)
    
    print(f"完成！所有 CSV 文件已保存到 {output_dir} 目录")
    
    # 显示统计信息
    print("\n统计信息:")
    for server_id in sorted(server_data.keys(), key=lambda x: int(x) if x.isdigit() else float('inf')):
        print(f"  server_id {server_id}: {len(server_data[server_id])} 条记录")

if __name__ == "__main__":
    # 执行分割
    split_dump_by_server_id("live_summary.dump")
