#!/usr/bin/env python3
"""
处理CSV文件，按照create_time的天单位聚合数据
- balance: 取平均值 (avg)
- profit: 取总和 (sum)
- profit_tj: 取总和 (sum)
- volume: 取总和 (sum)
- profit_all: 取最后一个值 (last)
"""

import pandas as pd
from datetime import datetime
import os

def process_csv_file(filename):
    """处理单个CSV文件"""
    print(f"处理文件: {filename}")
    
    # 读取CSV文件
    df = pd.read_csv(filename)
    
    # 将create_time转换为datetime
    df['datetime'] = pd.to_datetime(df['create_time'], unit='s')
    
    # 提取日期（年-月-日）
    df['date'] = df['datetime'].dt.date
    
    # 按日期分组并聚合
    aggregated = df.groupby('date').agg({
        'balance': 'mean',        # 平均值
        'profit': 'sum',          # 总和
        'profit_tj': 'sum',       # 总和
        'volume': 'sum',          # 总和
        'profit_all': 'last',     # 最后一个值
        'create_time': 'first'    # 保留第一个时间戳作为参考
    }).reset_index()
    
    # 重新排列列的顺序
    aggregated = aggregated[['date', 'create_time', 'balance', 'profit', 'profit_tj', 'volume', 'profit_all']]
    
    # 生成输出文件名
    base_name = os.path.splitext(filename)[0]
    output_filename = f"{base_name}_daily_aggregated.csv"
    
    # 保存结果
    aggregated.to_csv(output_filename, index=False)
    
    print(f"聚合结果已保存到: {output_filename}")
    print(f"原始数据行数: {len(df)}")
    print(f"聚合后行数: {len(aggregated)}")
    print(f"日期范围: {aggregated['date'].min()} 到 {aggregated['date'].max()}")
    print("-" * 50)
    
    return aggregated

def main():
    """主函数"""
    files_to_process = [
        'xiaoxinyuan_coinex.csv',
        'huangjinglong_funding.csv',
        'huangjinglong_mx.csv'
    ]
    
    results = {}
    
    for filename in files_to_process:
        if os.path.exists(filename):
            results[filename] = process_csv_file(filename)
        else:
            print(f"警告: 文件 {filename} 不存在")
    
    print("所有文件处理完成!")
    
    # 显示每个文件的汇总信息
    for filename, df in results.items():
        print(f"\n{filename} 汇总:")
        print(f"  总天数: {len(df)}")
        if len(df) > 0:
            print(f"  日期范围: {df['date'].min()} 到 {df['date'].max()}")
            print(f"  平均每日balance: {df['balance'].mean():.2f}")
            print(f"  总profit: {df['profit'].sum():.2f}")
            print(f"  总profit_tj: {df['profit_tj'].sum():.2f}")
            print(f"  总volume: {df['volume'].sum():.2f}")

if __name__ == "__main__":
    main()
