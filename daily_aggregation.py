#!/usr/bin/env python3
"""
将聚合后的CSV文件按天进行二次聚合
时间列显示为日期格式
"""

import pandas as pd
import os
from datetime import datetime

def aggregate_by_day(input_file, output_file):
    """
    将CSV文件按天聚合
    
    Args:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
    """
    print(f"正在处理文件: {input_file}")
    
    # 读取CSV文件
    df = pd.read_csv(input_file)
    
    # 转换时间戳为datetime
    df['datetime'] = pd.to_datetime(df['create_time'], unit='s')
    
    # 提取日期（年-月-日）
    df['date'] = df['datetime'].dt.date
    
    # 按日期分组并聚合数值列
    numeric_columns = ['balance', 'profit', 'profit_tj', 'volume', 'profit_all']
    
    # 确保这些列存在
    available_columns = [col for col in numeric_columns if col in df.columns]
    
    if not available_columns:
        print(f"  警告: 文件 {input_file} 中没有找到可聚合的数值列")
        return
    
    # 按日期分组聚合
    daily_agg = df.groupby('date')[available_columns].agg({
        'balance': 'mean',      # 余额取平均值
        'profit': 'sum',        # 利润求和
        'profit_tj': 'mean',    # profit_tj取平均值
        'volume': 'sum',        # 交易量求和
        'profit_all': 'last'    # 总利润取最后一个值
    }).reset_index()
    
    # 重命名列以避免多级索引
    daily_agg.columns = ['date'] + available_columns
    
    # 将日期转换为字符串格式
    daily_agg['date'] = daily_agg['date'].astype(str)
    
    # 添加记录数统计
    record_counts = df.groupby('date').size().reset_index(name='record_count')
    daily_agg = daily_agg.merge(record_counts, on='date', how='left')
    
    # 保存结果
    daily_agg.to_csv(output_file, index=False)
    
    print(f"  原始记录数: {len(df)}")
    print(f"  聚合后天数: {len(daily_agg)}")
    print(f"  时间范围: {daily_agg['date'].min()} - {daily_agg['date'].max()}")
    print(f"  保存到: {output_file}")
    print()
    
    return daily_agg

def process_all_aggregated_files():
    """
    处理所有聚合后的CSV文件
    """
    # 要处理的文件列表
    input_files = [
        'huangjinglong_mx.csv',
        'xiaoxinyuan_coinex.csv', 
        'huangjinglong_funding.csv'
    ]
    
    # 创建输出目录
    output_dir = 'daily_aggregated'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    print("开始按天聚合数据...")
    print("=" * 50)
    
    results = {}
    
    for input_file in input_files:
        if os.path.exists(input_file):
            # 生成输出文件名
            base_name = os.path.splitext(input_file)[0]
            output_file = os.path.join(output_dir, f"{base_name}_daily.csv")
            
            # 执行聚合
            result = aggregate_by_day(input_file, output_file)
            if result is not None:
                results[base_name] = result
        else:
            print(f"警告: 文件 {input_file} 不存在，跳过处理")
    
    print("=" * 50)
    print("聚合完成！")
    
    # 显示汇总统计
    print("\n汇总统计:")
    for name, df in results.items():
        print(f"\n{name}:")
        print(f"  天数: {len(df)}")
        print(f"  日期范围: {df['date'].min()} - {df['date'].max()}")
        if 'profit' in df.columns:
            print(f"  总利润: {df['profit'].sum():.2f}")
        if 'volume' in df.columns:
            print(f"  总交易量: {df['volume'].sum():.2f}")
        if 'balance' in df.columns:
            print(f"  平均余额: {df['balance'].mean():.2f}")
    
    return results

def create_sample_daily_view():
    """
    创建样本日数据查看
    """
    output_dir = 'daily_aggregated'
    
    print("\n样本数据预览:")
    print("=" * 50)
    
    for file in os.listdir(output_dir):
        if file.endswith('_daily.csv'):
            file_path = os.path.join(output_dir, file)
            df = pd.read_csv(file_path)
            
            print(f"\n{file}:")
            print(df.head(10))
            print(f"列名: {list(df.columns)}")

if __name__ == "__main__":
    # 执行按天聚合
    results = process_all_aggregated_files()
    
    # 显示样本数据
    create_sample_daily_view()
