{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Visualization Analysis\n", "\n", "This notebook visualizes the time series data from three aggregated CSV files."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import numpy as np\n", "\n", "# Set style\n", "plt.rcParams['font.sans-serif'] = ['DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "sns.set_style(\"whitegrid\")\n", "plt.rcParams['figure.figsize'] = (15, 8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read the three CSV files\n", "files = {\n", "    'HuangJingLong_MX': 'huangjinglong_mx.csv',\n", "    'Xiao<PERSON>in<PERSON><PERSON>_Coinex': 'xiaoxinyuan_coinex.csv',\n", "    'HuangJingLong_Funding': 'huangjinglong_funding.csv'\n", "}\n", "\n", "data = {}\n", "for name, file in files.items():\n", "    df = pd.read_csv(file)\n", "    # Convert timestamp to datetime\n", "    df['datetime'] = pd.to_datetime(df['create_time'], unit='s')\n", "    data[name] = df\n", "    print(f\"{name}: {len(df)} records\")\n", "    print(f\"Time range: {df['datetime'].min()} - {df['datetime'].max()}\")\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display basic information\n", "for name, df in data.items():\n", "    print(f\"=== {name} ===\")\n", "    print(df.head())\n", "    print(f\"Columns: {list(df.columns)}\")\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1. Balance Over Time\n", "plt.figure(figsize=(15, 8))\n", "\n", "colors = ['#1f77b4', '#ff7f0e', '#2ca02c']\n", "for i, (name, df) in enumerate(data.items()):\n", "    plt.plot(df['datetime'], df['balance'], \n", "             label=name, color=colors[i], linewidth=2, alpha=0.8)\n", "\n", "plt.title('Balance Over Time', fontsize=16, fontweight='bold')\n", "plt.xlabel('Time', fontsize=12)\n", "plt.ylabel('Balance', fontsize=12)\n", "plt.legend(fontsize=12)\n", "plt.grid(True, alpha=0.3)\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2. Profit Over Time\n", "plt.figure(figsize=(15, 8))\n", "\n", "for i, (name, df) in enumerate(data.items()):\n", "    plt.plot(df['datetime'], df['profit'], \n", "             label=name, color=colors[i], linewidth=2, alpha=0.8)\n", "\n", "plt.title('Profit Over Time', fontsize=16, fontweight='bold')\n", "plt.xlabel('Time', fontsize=12)\n", "plt.ylabel('Profit', fontsize=12)\n", "plt.legend(fontsize=12)\n", "plt.grid(True, alpha=0.3)\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3. Volume Over Time\n", "plt.figure(figsize=(15, 8))\n", "\n", "for i, (name, df) in enumerate(data.items()):\n", "    plt.plot(df['datetime'], df['volume'], \n", "             label=name, color=colors[i], linewidth=2, alpha=0.8)\n", "\n", "plt.title('Volume Over Time', fontsize=16, fontweight='bold')\n", "plt.xlabel('Time', fontsize=12)\n", "plt.ylabel('Volume', fontsize=12)\n", "plt.legend(fontsize=12)\n", "plt.grid(True, alpha=0.3)\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 4. Comprehensive Comparison\n", "fig, axes = plt.subplots(2, 2, figsize=(20, 12))\n", "fig.suptitle('Comprehensive Analysis', fontsize=18, fontweight='bold')\n", "\n", "# Balance comparison\n", "for i, (name, df) in enumerate(data.items()):\n", "    axes[0, 0].plot(df['datetime'], df['balance'], \n", "                    label=name, color=colors[i], linewidth=2, alpha=0.8)\n", "axes[0, 0].set_title('Balance', fontsize=14)\n", "axes[0, 0].set_ylabel('Balance', fontsize=12)\n", "axes[0, 0].legend()\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Profit comparison\n", "for i, (name, df) in enumerate(data.items()):\n", "    axes[0, 1].plot(df['datetime'], df['profit'], \n", "                    label=name, color=colors[i], linewidth=2, alpha=0.8)\n", "axes[0, 1].set_title('Profit', fontsize=14)\n", "axes[0, 1].set_ylabel('Profit', fontsize=12)\n", "axes[0, 1].legend()\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# Volume comparison\n", "for i, (name, df) in enumerate(data.items()):\n", "    axes[1, 0].plot(df['datetime'], df['volume'], \n", "                    label=name, color=colors[i], linewidth=2, alpha=0.8)\n", "axes[1, 0].set_title('Volume', fontsize=14)\n", "axes[1, 0].set_ylabel('Volume', fontsize=12)\n", "axes[1, 0].set_xlabel('Time', fontsize=12)\n", "axes[1, 0].legend()\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# Total profit comparison\n", "for i, (name, df) in enumerate(data.items()):\n", "    axes[1, 1].plot(df['datetime'], df['profit_all'], \n", "                    label=name, color=colors[i], linewidth=2, alpha=0.8)\n", "axes[1, 1].set_title('Total Profit', fontsize=14)\n", "axes[1, 1].set_ylabel('Total Profit', fontsize=12)\n", "axes[1, 1].set_xlabel('Time', fontsize=12)\n", "axes[1, 1].legend()\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "# Adjust x-axis labels\n", "for ax in axes.flat:\n", "    ax.tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 5. Data Summary Statistics\n", "print(\"=== Data Summary Statistics ===\")\n", "for name, df in data.items():\n", "    print(f\"\\n{name}:\")\n", "    print(f\"  Records: {len(df)}\")\n", "    print(f\"  Time span: {(df['datetime'].max() - df['datetime'].min()).days} days\")\n", "    print(f\"  Average balance: {df['balance'].mean():.2f}\")\n", "    print(f\"  Average profit: {df['profit'].mean():.2f}\")\n", "    print(f\"  Total volume: {df['volume'].sum():.2f}\")\n", "    print(f\"  Final total profit: {df['profit_all'].iloc[-1]:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 6. Cumulative Profit Over Time\n", "plt.figure(figsize=(15, 8))\n", "\n", "for i, (name, df) in enumerate(data.items()):\n", "    # Calculate cumulative profit\n", "    cumulative_profit = df['profit'].cumsum()\n", "    plt.plot(df['datetime'], cumulative_profit, \n", "             label=f'{name} (Cumulative)', color=colors[i], linewidth=2, alpha=0.8)\n", "\n", "plt.title('Cumulative Profit Over Time', fontsize=16, fontweight='bold')\n", "plt.xlabel('Time', fontsize=12)\n", "plt.ylabel('Cumulative Profit', fontsize=12)\n", "plt.legend(fontsize=12)\n", "plt.grid(True, alpha=0.3)\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}