import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import numpy as np
import matplotlib.font_manager as fm

# 设置中文字体和图表样式
# 尝试多种中文字体
chinese_fonts = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'DejaVu Sans']
for font in chinese_fonts:
    try:
        plt.rcParams['font.sans-serif'] = [font]
        break
    except:
        continue

# 如果以上字体都不可用，使用系统默认字体
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
plt.rcParams['figure.figsize'] = (15, 8)

# 检查可用的中文字体
print("可用的字体:")
font_list = [f.name for f in fm.fontManager.ttflist if 'SimHei' in f.name or 'YaHei' in f.name or 'WenQuanYi' in f.name]
if font_list:
    print(f"找到中文字体: {font_list}")
    plt.rcParams['font.sans-serif'] = [font_list[0]]
else:
    print("未找到中文字体，将使用英文标签")

print(f"当前使用字体: {plt.rcParams['font.sans-serif']}")

# 读取三个CSV文件
files = {
    'huangjinglong_mx': 'huangjinglong_mx.csv',
    'xiaoxinyuan_coinex': 'xiaoxinyuan_coinex.csv',
    'huangjinglong_funding': 'huangjinglong_funding.csv'
}

data = {}
for name, file in files.items():
    df = pd.read_csv(file)
    # 转换时间戳为datetime
    df['datetime'] = pd.to_datetime(df['create_time'], unit='s')
    data[name] = df
    print(f"{name}: {len(df)} 条记录")
    print(f"时间范围: {df['datetime'].min()} - {df['datetime'].max()}")
    print()

# 查看数据基本信息
for name, df in data.items():
    print(f"=== {name} ===")
    print(df.head())
    print(f"数据列: {list(df.columns)}")
    print()

# 1. 余额(balance)时间线图
plt.figure(figsize=(15, 8))

colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
for i, (name, df) in enumerate(data.items()):
    plt.plot(df['datetime'], df['balance'],
             label=name, color=colors[i], linewidth=2, alpha=0.8)

plt.title('余额变化时间线', fontsize=16, fontweight='bold')
plt.xlabel('时间', fontsize=12)
plt.ylabel('余额', fontsize=12)
plt.legend(fontsize=12)
plt.grid(True, alpha=0.3)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# 2. 利润(profit)时间线图
plt.figure(figsize=(15, 8))

for i, (name, df) in enumerate(data.items()):
    plt.plot(df['datetime'], df['profit'],
             label=name, color=colors[i], linewidth=2, alpha=0.8)

plt.title('利润变化时间线', fontsize=16, fontweight='bold')
plt.xlabel('时间', fontsize=12)
plt.ylabel('利润', fontsize=12)
plt.legend(fontsize=12)
plt.grid(True, alpha=0.3)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# 3. 交易量(volume)时间线图
plt.figure(figsize=(15, 8))

for i, (name, df) in enumerate(data.items()):
    plt.plot(df['datetime'], df['volume'],
             label=name, color=colors[i], linewidth=2, alpha=0.8)

plt.title('交易量变化时间线', fontsize=16, fontweight='bold')
plt.xlabel('时间', fontsize=12)
plt.ylabel('交易量', fontsize=12)
plt.legend(fontsize=12)
plt.grid(True, alpha=0.3)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# 4. 综合对比图 - 子图形式
fig, axes = plt.subplots(2, 2, figsize=(20, 12))
fig.suptitle('三个数据集综合对比分析', fontsize=18, fontweight='bold')

# 余额对比
for i, (name, df) in enumerate(data.items()):
    axes[0, 0].plot(df['datetime'], df['balance'],
                    label=name, color=colors[i], linewidth=2, alpha=0.8)
axes[0, 0].set_title('余额变化', fontsize=14)
axes[0, 0].set_ylabel('余额', fontsize=12)
axes[0, 0].legend()
axes[0, 0].grid(True, alpha=0.3)

# 利润对比
for i, (name, df) in enumerate(data.items()):
    axes[0, 1].plot(df['datetime'], df['profit'],
                    label=name, color=colors[i], linewidth=2, alpha=0.8)
axes[0, 1].set_title('利润变化', fontsize=14)
axes[0, 1].set_ylabel('利润', fontsize=12)
axes[0, 1].legend()
axes[0, 1].grid(True, alpha=0.3)

# 交易量对比
for i, (name, df) in enumerate(data.items()):
    axes[1, 0].plot(df['datetime'], df['volume'],
                    label=name, color=colors[i], linewidth=2, alpha=0.8)
axes[1, 0].set_title('交易量变化', fontsize=14)
axes[1, 0].set_ylabel('交易量', fontsize=12)
axes[1, 0].set_xlabel('时间', fontsize=12)
axes[1, 0].legend()
axes[1, 0].grid(True, alpha=0.3)

# 总利润对比
for i, (name, df) in enumerate(data.items()):
    axes[1, 1].plot(df['datetime'], df['profit_all'],
                    label=name, color=colors[i], linewidth=2, alpha=0.8)
axes[1, 1].set_title('总利润变化', fontsize=14)
axes[1, 1].set_ylabel('总利润', fontsize=12)
axes[1, 1].set_xlabel('时间', fontsize=12)
axes[1, 1].legend()
axes[1, 1].grid(True, alpha=0.3)

# 调整x轴标签角度
for ax in axes.flat:
    ax.tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# 5. 数据统计摘要
print("=== 数据统计摘要 ===")
for name, df in data.items():
    print(f"\n{name}:")
    print(f"  记录数量: {len(df)}")
    print(f"  时间跨度: {(df['datetime'].max() - df['datetime'].min()).days} 天")
    print(f"  平均余额: {df['balance'].mean():.2f}")
    print(f"  平均利润: {df['profit'].mean():.2f}")
    print(f"  总交易量: {df['volume'].sum():.2f}")
    print(f"  最终总利润: {df['profit_all'].iloc[-1]:.2f}")

# 6. 利润累积图
plt.figure(figsize=(15, 8))

for i, (name, df) in enumerate(data.items()):
    # 计算累积利润
    cumulative_profit = df['profit'].cumsum()
    plt.plot(df['datetime'], cumulative_profit,
             label=f'{name} (累积利润)', color=colors[i], linewidth=2, alpha=0.8)

plt.title('累积利润变化时间线', fontsize=16, fontweight='bold')
plt.xlabel('时间', fontsize=12)
plt.ylabel('累积利润', fontsize=12)
plt.legend(fontsize=12)
plt.grid(True, alpha=0.3)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()